from typing import Dict, List, Optional
from pydantic import BaseModel


class Tools(BaseModel):
    memory: Optional[str] = None
    external_tools: str


class PipelineStep(BaseModel):
    step: str
    process: str
    agent: str  # REQUIRED: Name of the agent to use for this step
    input: Dict[str, str]
    tools: Tools
    output: Dict[str, str]


class OnInterrupt(BaseModel):
    handler: str
    resume_from: str


class OnError(BaseModel):
    retry: int
    fallback_state: str


class Layer2(BaseModel):
    id: str
    version: str
    pipeline: List[PipelineStep]
    onInterrupt: Optional[OnInterrupt] = None
    onError: Optional[OnError] = None
