import asyncio
import json
import os
from typing import Dict, Any, Optional, List
from utils.redis_client import RedisClient
from core.logger_config import get_module_logger
import openai

class OrchestratorV3:
    def __init__(self, session_id: str, workflow_name: str, state_manager, memory_manager, redis_client: RedisClient):
        self.session_id = session_id
        self.workflow_name = workflow_name
        self.state_manager = state_manager
        self.memory_manager = memory_manager
        self.redis_client = redis_client
        self.logger = get_module_logger("orchestrator_v3", session_id=session_id)
        self.prohibited_actions = None
        self.agent_response_cache: Dict[str, List[Dict[str, Any]]] = {}  # state_id -> list of responses
        self.workflow_summary = None
        self.status = "initialized"
        self.reason = None
        self.key_events = []

    async def initialize(self):
        # Cache prohibited actions at start
        if self.prohibited_actions is None:
            self.prohibited_actions = await self.state_manager.getProhibitedActions()
        # Preload agent responses for this session
        await self._populate_agent_response_cache()
        self.logger.info("OrchestratorV3 initialized", action="initialize", layer="orchestrator_v3", step="init")

    async def _populate_agent_response_cache(self):
        """Parse the conversations log and cache agent responses for this session."""
        log_path = os.path.join("logs", "conversations", "conversations.jsonl")
        if not os.path.exists(log_path):
            return
        self.agent_response_cache.clear()
        try:
            with open(log_path, "r", encoding="utf-8") as f:
                for line in f:
                    try:
                        entry = json.loads(line)
                        if entry.get("session_id") == self.session_id:
                            state_id = entry.get("state_id", "unknown")
                            if state_id not in self.agent_response_cache:
                                self.agent_response_cache[state_id] = []
                            self.agent_response_cache[state_id].append(entry)
                    except Exception:
                        continue
        except Exception as e:
            self.logger.error(f"Failed to populate agent response cache: {e}", action="populate_agent_response_cache", layer="orchestrator_v3")

    async def get_user_query(self, retries=3) -> Optional[str]:
        for attempt in range(retries):
            clean_text = await self.memory_manager.get("clean_text")
            if clean_text:
                return clean_text
            await asyncio.sleep(0.5)
        self.logger.error("User query (clean_text) not found after retries", action="get_user_query", layer="orchestrator_v3")
        return None

    async def get_agent_responses(self, state_id: str) -> List[str]:
        # Return all agent outputs for this state from the cache
        responses = []
        for entry in self.agent_response_cache.get(state_id, []):
            output = entry.get("output")
            if output:
                if isinstance(output, dict) and "llm_answer" in output:
                    responses.append(output["llm_answer"])
                elif isinstance(output, str):
                    responses.append(output)
        return responses

    async def get_agent_confidence(self, agent_name: str) -> Optional[float]:
        # Try to get confidence from Redis (publish/subscribe or key)
        try:
            key = f"session:{self.session_id}:agent:{agent_name}:confidence"
            confidence = await self.redis_client.get(key)
            if confidence is not None:
                try:
                    return float(confidence)
                except Exception:
                    return None
            self.logger.warning(f"Confidence value missing for agent {agent_name}", action="get_agent_confidence", layer="orchestrator_v3")
        except Exception as e:
            self.logger.warning(f"Error retrieving confidence for agent {agent_name}: {e}", action="get_agent_confidence", layer="orchestrator_v3")
        return None

    async def get_state_summary(self) -> str:
        # Get workflow and pipeline state summaries
        workflow_state = await self.state_manager.getCurrentWorkflowState()
        pipeline_state = await self.state_manager.getCurrentPipelineState()
        allowed_actions = await self.state_manager.getAllowedActions()
        summary = (
            f"Workflow: {self.workflow_name}\n"
            f"Current Workflow State: {workflow_state}\n"
            f"Current Pipeline State: {pipeline_state}\n"
            f"Allowed Actions: {allowed_actions}\n"
            f"Prohibited Actions: {self.prohibited_actions}"
        )
        return summary

    async def evaluate_with_llm(self, user_query: str, agent_responses: List[str], agent_confidence: Optional[float], state_summary: str) -> str:
        prompt = (
            "You are an AI orchestrator evaluating a conversation state. Based on the following information, decide whether to PROCEED to the next state or REDO the current state.\n"
            f"State Summary: {state_summary}\n"
            f"User Query: {user_query if user_query is not None else '[Not available]'}\n"
            f"Agent Responses: {agent_responses}\n"
            f"Agent Confidence: {agent_confidence}\n"
            "If the user query is not available, make your best decision based on the other information.\n"
            "Respond with ONLY one word: 'proceed' or 'redo'."
        )
        print("[DEBUG] LLM prompt:", prompt)
        print("[DEBUG] LLM inputs:", {
            "user_query": user_query,
            "agent_responses": agent_responses,
            "agent_confidence": agent_confidence,
            "state_summary": state_summary
        })
        try:
            client = openai.AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))
            response = await client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=10,
                temperature=0.1
            )
            decision = response.choices[0].message.content.strip().lower()
            print("[DEBUG] LLM raw response:", response)
            return "proceed" if "proceed" in decision else "redo"
        except Exception as e:
            print(f"[DEBUG] LLM evaluation failed, defaulting to 'redo': {e}")
            self.logger.error(f"LLM evaluation failed: {e}", action="evaluate_with_llm", layer="orchestrator_v3")
            return "redo"

    async def run(self):
        await self.initialize()
        self.status = "running"
        user_query_found = False
        try:
            while True:
                # Always re-fetch the current pipeline state and pipeline at the start of each iteration
                state_summary = await self.get_state_summary()
                pipeline_state = await self.state_manager.getCurrentPipelineState()
                raw_pipeline = await self.state_manager.getCurrentPipeline()
                # Fix: get the actual list of step objects
                if hasattr(raw_pipeline, 'pipeline'):
                    print("[DEBUG] getCurrentPipeline() returned Layer2 object, using .pipeline attribute")
                    current_pipeline = raw_pipeline.pipeline
                else:
                    print("[DEBUG] getCurrentPipeline() returned list directly")
                    current_pipeline = raw_pipeline
                print("[DEBUG] type(current_pipeline):", type(current_pipeline))
                if hasattr(current_pipeline, '__len__') and len(current_pipeline) > 0:
                    print("[DEBUG] type(current_pipeline[0]):", type(current_pipeline[0]))
                    print("[DEBUG] current_pipeline[0]:", current_pipeline[0])
                print("[DEBUG] Current pipeline state:", pipeline_state)
                # Defensive: Check current_pipeline is not None and is iterable
                if current_pipeline is None:
                    self.logger.error("Current pipeline is None. Aborting.")
                    return {'status': 'aborted', 'reason': 'Current pipeline is None.'}
                if not hasattr(current_pipeline, '__iter__'):
                    self.logger.error(f"Current pipeline is not iterable: {type(current_pipeline)}. Aborting.")
                    return {'status': 'aborted', 'reason': f'Current pipeline is not iterable: {type(current_pipeline)}'}
                # Defensive: Check pipeline_state is not None
                if pipeline_state is None:
                    self.logger.error("Pipeline state is None. Aborting.")
                    return {'status': 'aborted', 'reason': 'Pipeline state is None.'}
                # Use attribute access for PipelineStep objects
                current_step = None
                for step in current_pipeline:
                    if step is None:
                        self.logger.warning("Encountered None in current_pipeline steps. Skipping.")
                        continue
                    if hasattr(step, "step") and step.step == pipeline_state:
                        current_step = step
                        break
                if not current_step:
                    self.logger.error(f"No pipeline step found for state: {pipeline_state}. Aborting.")
                    return {'status': 'aborted', 'reason': f'No pipeline step found for state: {pipeline_state}'}
                # Defensive: Check current_step.input and current_step.output are dicts
                if not isinstance(current_step.input, dict):
                    self.logger.error(f"current_step.input is not a dict: {type(current_step.input)}. Aborting.")
                    return {'status': 'aborted', 'reason': f'current_step.input is not a dict: {type(current_step.input)}'}
                if not isinstance(current_step.output, dict):
                    self.logger.error(f"current_step.output is not a dict: {type(current_step.output)}. Aborting.")
                    return {'status': 'aborted', 'reason': f'current_step.output is not a dict: {type(current_step.output)}'}
                # Get agent names (support string or list for future-proofing)
                agent_names = [current_step.agent] if isinstance(current_step.agent, str) else list(current_step.agent)
                # Defensive: Check agent_names is a list and not None
                if agent_names is None or not isinstance(agent_names, list):
                    self.logger.error(f"agent_names is not a list: {type(agent_names)}. Aborting.")
                    return {'status': 'aborted', 'reason': f'agent_names is not a list: {type(agent_names)}'}
                # Build input_data from contextual memory using step.input dict
                input_data = {}
                for input_key, mem_key in current_step.input.items():
                    # Try to get from contextual memory first
                    value = await self.memory_manager.get(mem_key)
                    # If not found, try to get from shared context (Redis)
                    if value is None:
                        shared_context = await self.memory_manager.contextual.get_all()
                        value = shared_context.get(mem_key)
                    # If still not found, try to get from previous step's output (auto-mapping)
                    if value is None and 'last_step_output' in locals():
                        value = last_step_output.get(input_key) or last_step_output.get(mem_key)
                        if value is not None:
                            print(f"[DEBUG] Auto-mapped missing input '{mem_key}' for step '{current_step.step}' from previous step output.")
                    if value is None:
                        print(f"[DEBUG] Missing required input '{mem_key}' for step '{current_step.step}' even after auto-mapping.")
                        return {'status': 'aborted', 'reason': f"Missing required input '{mem_key}' for step '{current_step.step}'"}
                    input_data[input_key] = value
                print(f"[DEBUG] input_data for step '{current_step.step}':", input_data)
                # Execute the pipeline step
                try:
                    result = await self.state_manager.executePipelineState(input_data)
                except Exception as e:
                    print(f"[DEBUG] Exception during executePipelineState: {e}")
                    return {'status': 'aborted', 'reason': f"Exception during executePipelineState: {e}"}
                # Defensive: Check result is not None before accessing output
                if result is None:
                    self.logger.warning(f"Result from executePipelineState is None for step '{current_step.step}'. Attempting fallback.")
                    result = {}
                # Universal output handling: convert to dict if needed
                output = None
                if isinstance(result, dict):
                    output = result.get('output') if 'output' in result else result
                elif hasattr(result, 'dict') and callable(getattr(result, 'dict')):
                    print(f"[DEBUG] Agent output for step '{current_step.step}' is a Pydantic model, using .dict()")
                    result_dict = result.dict()
                    output = result_dict.get('output') if 'output' in result_dict else result_dict
                elif hasattr(result, 'model_dump') and callable(getattr(result, 'model_dump')):
                    print(f"[DEBUG] Agent output for step '{current_step.step}' is a Pydantic v2 model, using .model_dump()")
                    result_dict = result.model_dump()
                    output = result_dict.get('output') if 'output' in result_dict else result_dict
                else:
                    print(f"[DEBUG] Agent output for step '{current_step.step}' is of type {type(result)}, attempting to use as dict.")
                    try:
                        output = dict(result)
                    except Exception as e:
                        self.logger.error(f"Agent output for step '{current_step.step}' could not be converted to dict: {e}")
                        raise RuntimeError(f"Agent output for step '{current_step.step}' could not be converted to dict: {e}")
                if output is None:
                    self.logger.warning(f"Agent output for step '{current_step.step}' is None. Attempting to fetch from memory/context as fallback.")
                    # Try to fetch expected outputs from memory/context
                    output = {}
                    for out_key, mem_key in current_step.output.items():
                        value = await self.memory_manager.get(mem_key)
                        if value is not None:
                            output[out_key] = value
                    if not output:
                        self.logger.error(f"No output found in memory/context for step '{current_step.step}'. Aborting pipeline.")
                        raise RuntimeError(f"No output for step '{current_step.step}' in agent return or memory/context.")
                # Save output for auto-mapping in next step
                last_step_output = output
                # Defensive: Check output is a dict or iterable before iterating/accessing
                if output is None or (not hasattr(output, '__iter__') and not isinstance(output, dict)):
                    self.logger.error(f"Output for step '{current_step.step}' is not iterable or dict. Aborting.")
                    return {'status': 'aborted', 'reason': f"Output for step '{current_step.step}' is not iterable or dict."}
                # Now safe to iterate or access output
                # Try to fetch user query (clean_text) as the very last thing before evaluation
                # Try contextual memory first, then shared context
                user_query = await self.memory_manager.get('clean_text')
                if user_query is None:
                    shared_context = await self.memory_manager.contextual.get_all()
                    user_query = shared_context.get('clean_text')

                if user_query is None:
                    print("[DEBUG] clean_text not found, skipping LLM evaluation and proceeding to next pipeline step.")
                    # Find the next pipeline step
                    next_step_id = None
                    for idx, step in enumerate(current_pipeline):
                        if hasattr(step, "step") and step.step == pipeline_state:
                            if idx + 1 < len(current_pipeline):
                                next_step_id = current_pipeline[idx + 1].step
                            break
                    if next_step_id:
                        self.logger.info(f"Transitioning to next pipeline step (no user_query): {next_step_id}")
                        transitioned = await self.state_manager.transitionPipeline(next_step_id)
                    else:
                        self.logger.info("No more pipeline steps (no user_query), attempting workflow transition.")
                        transitioned = await self.state_manager.transitionWorkflow(None)
                    pipeline_state_after = await self.state_manager.getCurrentPipelineState()
                    print(f"[DEBUG] pipeline_state after transition (no user_query): {pipeline_state_after}")
                    if not transitioned:
                        print("[DEBUG] Pipeline completed (no user_query), breaking loop.")
                        self.status = "completed"
                        self.reason = "Workflow completed successfully (no user_query)."
                        self.key_events.append(self.reason)
                        break
                    continue  # Go to next loop iteration

                # Get agent responses and confidences for all agents in the current pipeline state
                agent_responses_list = []
                agent_confidences_list = []
                for agent_name in agent_names:
                    agent_responses_list.append(await self.get_agent_responses(agent_name))
                    agent_confidences_list.append(await self.get_agent_confidence(agent_name))

                # Defensive: Check agent_responses_list and agent_confidences_list are lists
                if agent_responses_list is None or not isinstance(agent_responses_list, list):
                    self.logger.error("agent_responses_list is not a list. Aborting.")
                    return {'status': 'aborted', 'reason': 'agent_responses_list is not a list.'}
                if agent_confidences_list is None or not isinstance(agent_confidences_list, list):
                    self.logger.error("agent_confidences_list is not a list. Aborting.")
                    return {'status': 'aborted', 'reason': 'agent_confidences_list is not a list.'}
                # Defensive: Flatten lists only if they are lists of lists
                all_agent_responses = []
                for sublist in agent_responses_list:
                    if sublist is None:
                        self.logger.warning("None found in agent_responses_list. Skipping.")
                        continue
                    if isinstance(sublist, list):
                        all_agent_responses.extend(sublist)
                    else:
                        all_agent_responses.append(sublist)
                all_agent_confidences = []
                for sublist in agent_confidences_list:
                    if sublist is None:
                        self.logger.warning("None found in agent_confidences_list. Skipping.")
                        continue
                    if isinstance(sublist, list):
                        all_agent_confidences.extend(sublist)
                    else:
                        all_agent_confidences.append(sublist)

                print(f"[DEBUG] pipeline_state before LLM evaluation: {pipeline_state}")
                decision = await self.evaluate_with_llm(user_query, all_agent_responses, all_agent_confidences, state_summary)
                print(f"[DEBUG] LLM decision: {decision}")
                self.key_events.append(f"Evaluated state {pipeline_state}: {decision}")
                if decision == "proceed":
                    # Try to transition to next pipeline state
                    try:
                        # Find the next pipeline step
                        next_step_id = None
                        for idx, step in enumerate(current_pipeline):
                            if hasattr(step, "step") and step.step == pipeline_state:
                                if idx + 1 < len(current_pipeline):
                                    next_step_id = current_pipeline[idx + 1].step
                                break
                        if next_step_id:
                            self.logger.info(f"Transitioning to next pipeline step: {next_step_id}")
                            transitioned = await self.state_manager.transitionPipeline(next_step_id)
                        else:
                            self.logger.info("No more pipeline steps, attempting workflow transition.")
                            transitioned = await self.state_manager.transitionWorkflow(None)
                        # Re-fetch pipeline_state after transition
                        pipeline_state_after = await self.state_manager.getCurrentPipelineState()
                        print(f"[DEBUG] pipeline_state after transition: {pipeline_state_after}")
                        if not transitioned:
                            print("[DEBUG] Pipeline completed, breaking loop.")
                            self.status = "completed"
                            self.reason = "Workflow completed successfully."
                            self.key_events.append(self.reason)
                            break
                    except Exception as e:
                        self.logger.error(f"Transition failed: {e}", action="run", layer="orchestrator_v3")
                        # Retry once
                        try:
                            if next_step_id:
                                transitioned = await self.state_manager.transitionPipeline(next_step_id)
                            else:
                                transitioned = await self.state_manager.transitionWorkflow(None)
                            pipeline_state_after = await self.state_manager.getCurrentPipelineState()
                            print(f"[DEBUG] pipeline_state after retry transition: {pipeline_state_after}")
                            if not transitioned:
                                print("[DEBUG] Pipeline completed after retry, breaking loop.")
                                self.status = "aborted"
                                self.reason = f"Transition failed after retry: {e}"
                                self.key_events.append(self.reason)
                                break
                        except Exception as e2:
                            print("[DEBUG] Pipeline transition failed after retry, breaking loop.")
                            self.status = "aborted"
                            self.reason = f"Transition failed after retry: {e2}"
                            self.key_events.append(self.reason)
                            break
                else:
                    print(f"[DEBUG] LLM decision was not 'proceed' (was '{decision}'), breaking loop.")
                    # LLM said to redo; abort after one redo for now
                    self.status = "aborted"
                    self.reason = "LLM evaluation requested redo; aborting."
                    self.key_events.append(self.reason)
                    break
        except Exception as e:
            self.status = "aborted"
            self.reason = f"Exception in orchestrator run: {str(e)}"
            return {"status": self.status, "reason": self.reason, "key_events": [self.reason]}
        # Reference: see scripts/test_state_manager_agents_v2.py for correct state manager usage

    async def cleanup(self):
        # Stub for orchestrator cleanup logic
        pass 