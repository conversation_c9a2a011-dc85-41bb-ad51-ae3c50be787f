{"id": "l2_more_help", "version": "1.0", "pipeline": [{"step": "stt", "process": "stt_process", "agent": "stt_agent", "input": {"audio": "audio_path"}, "tools": {"external_tools": "stt"}, "output": {"text": "text", "latencySTT": "latencySTT", "audio_path": "audio_path"}}, {"step": "preprocessing", "process": "preprocessing_process", "agent": "preprocessing_agent", "input": {"text": "text"}, "tools": {"external_tools": "openai"}, "output": {"user_response": "user_response", "latencyPreprocessing": "latencyPreprocessing", "fallback_message": "fallback_message"}}, {"step": "processing", "process": "processing_process", "agent": "processing_agent", "input": {"user_response": "user_response"}, "tools": {"external_tools": "context_analyzer"}, "output": {"routing_decision": "routing_decision", "suggested_services": "suggested_services", "latencyProcessing": "latencyProcessing"}}, {"step": "tts", "process": "tts_process", "agent": "tts_agent", "input": {"text": "routing_decision"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyTTS": "latencyTTS"}}], "onInterrupt": {"handler": "interrupt_manager", "resume_from": "stt"}, "onError": {"retry": 2, "fallback_state": "l2_fallback_more_help"}, "outputs": ["acknowledgement_message"]}