{"id": "l2_account_balance", "version": "1.0", "pipeline": [{"step": "stt", "process": "stt_process", "agent": "stt_agent", "input": {"audio_path": "audio_path"}, "tools": {"external_tools": "stt"}, "output": {"text": "text", "latencySTT": "latencySTT", "audio_path": "audio_path"}}, {"step": "preprocessing", "process": "preprocessing_process", "agent": "preprocessing_agent", "input": {"text": "text"}, "tools": {"external_tools": "openai"}, "output": {"account_id": "account_id", "latencyPreprocessing": "latencyPreprocessing", "clean_text": "clean_text", "fallback_message": "fallback_message"}}, {"step": "processing", "process": "processing_process", "agent": "processing_agent", "input": {"account_id": "account_id"}, "tools": {"external_tools": "banking_api"}, "output": {"account_balance": "account_balance", "latencyProcessing": "latencyProcessing"}}, {"step": "tts", "process": "tts_process", "agent": "tts_agent", "input": {"text": "account_balance"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyTTS": "latencyTTS"}}], "onInterrupt": {"handler": "interrupt_manager", "resume_from": "stt"}, "onError": {"retry": 3, "fallback_state": "l2_fallback_balance"}, "outputs": ["account_balance", "confirmation_message"]}