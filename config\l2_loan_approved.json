{"id": "l2_loan_approved", "version": "1.0", "pipeline": [{"step": "stt", "process": "stt_process", "agent": "stt_agent", "input": {"audio": "audio_path"}, "tools": {"external_tools": "stt"}, "output": {"text": "text", "latencySTT": "latencySTT", "audio_path": "audio_path"}}, {"step": "preprocessing", "process": "preprocessing_process", "agent": "preprocessing_agent", "input": {"text": "text"}, "tools": {"external_tools": "openai"}, "output": {"acceptance_confirmation": "acceptance_confirmation", "latencyPreprocessing": "latencyPreprocessing", "fallback_message": "fallback_message"}}, {"step": "processing", "process": "processing_process", "agent": "processing_agent", "input": {"acceptance_confirmation": "acceptance_confirmation"}, "tools": {"external_tools": "document_generator"}, "output": {"next_steps": "next_steps", "document_reference": "document_reference", "latencyProcessing": "latencyProcessing"}}, {"step": "tts", "process": "tts_process", "agent": "tts_agent", "input": {"text": "next_steps"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyTTS": "latencyTTS"}}], "onInterrupt": {"handler": "interrupt_manager", "resume_from": "stt"}, "onError": {"retry": 2, "fallback_state": "l2_fallback_approved"}, "outputs": ["congratulations_message"]}